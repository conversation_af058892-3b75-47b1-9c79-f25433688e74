{"name": "web", "private": true, "type": "module", "scripts": {"build": "vite build", "serve": "vite preview", "dev": "vite dev --port=3001"}, "dependencies": {"@convex-dev/react-query": "^0.0.0-alpha.8", "@kosh/backend": "workspace:*", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-form": "^1.0.5", "@tanstack/react-query": "^5.80.6", "@tanstack/react-router": "^1.121.0-alpha.27", "@tanstack/react-router-with-query": "^1.121.0", "@tanstack/react-start": "^1.121.0-alpha.27", "@tanstack/router-plugin": "^1.121.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.25.4", "lucide-react": "^0.525.0", "radix-ui": "^1.4.2", "react": "19.0.0", "react-dom": "19.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "vite-tsconfig-paths": "^5.1.4", "zod": "^4.0.2"}, "devDependencies": {"@tanstack/react-router-devtools": "^1.121.0-alpha.27", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.5.2", "jsdom": "^26.0.0", "typescript": "^5.7.2", "vite": "^7.0.2", "web-vitals": "^5.0.3"}}